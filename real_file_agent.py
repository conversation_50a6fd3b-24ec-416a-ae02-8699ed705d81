#!/usr/bin/env python3
"""
真实的文件操作代理 - 支持人机协作的文件管理系统
"""

import os
import shutil
import json
from pathlib import Path
from typing import Literal, List
from datetime import datetime

from langgraph.graph import StateGraph, END, MessagesState
from langgraph.prebuilt import ToolNode
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.messages import SystemMessage, ToolMessage, HumanMessage
from langchain_core.tools import tool
from langchain.chat_models import init_chat_model

# 设置Google API密钥
import os
GOOGLE_API_KEY = "AIzaSyCRMfVZuUDwLGH2gMDNYV5sawu5TbpMZSI"
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY

# 初始化LLM
llm = init_chat_model("gemini-2.0-flash", model_provider="google_genai")

# ==================== 真实的文件操作工具 ====================

@tool
def list_files(directory: str = ".") -> str:
    """列出指定目录中的文件和文件夹"""
    try:
        path = Path(directory)
        if not path.exists():
            return f"错误：目录 '{directory}' 不存在"
        
        if not path.is_dir():
            return f"错误：'{directory}' 不是一个目录"
        
        items = []
        for item in sorted(path.iterdir()):
            if item.is_dir():
                items.append(f"📁 {item.name}/")
            else:
                size = item.stat().st_size
                items.append(f"📄 {item.name} ({size} bytes)")
        
        if not items:
            return f"目录 '{directory}' 是空的"
        
        return f"目录 '{directory}' 的内容:\n" + "\n".join(items)
    
    except PermissionError:
        return f"错误：没有权限访问目录 '{directory}'"
    except Exception as e:
        return f"错误：{str(e)}"

@tool
def read_file(filepath: str) -> str:
    """读取文件内容"""
    try:
        path = Path(filepath)
        if not path.exists():
            return f"错误：文件 '{filepath}' 不存在"
        
        if not path.is_file():
            return f"错误：'{filepath}' 不是一个文件"
        
        with open(path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return f"文件 '{filepath}' 的内容:\n{content}"
    
    except PermissionError:
        return f"错误：没有权限读取文件 '{filepath}'"
    except UnicodeDecodeError:
        return f"错误：文件 '{filepath}' 不是文本文件或编码不支持"
    except Exception as e:
        return f"错误：{str(e)}"

@tool
def create_file(filepath: str, content: str = "") -> str:
    """创建新文件"""
    try:
        path = Path(filepath)
        
        # 创建父目录（如果不存在）
        path.parent.mkdir(parents=True, exist_ok=True)
        
        if path.exists():
            return f"错误：文件 '{filepath}' 已存在"
        
        with open(path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return f"成功创建文件 '{filepath}'"
    
    except PermissionError:
        return f"错误：没有权限创建文件 '{filepath}'"
    except Exception as e:
        return f"错误：{str(e)}"

@tool
def delete_file(filepath: str) -> str:
    """删除文件 - 危险操作！"""
    try:
        path = Path(filepath)
        if not path.exists():
            return f"错误：文件 '{filepath}' 不存在"
        
        if not path.is_file():
            return f"错误：'{filepath}' 不是一个文件"
        
        # 创建备份
        backup_dir = Path("./file_backups")
        backup_dir.mkdir(exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = backup_dir / f"{path.name}_{timestamp}.backup"
        
        shutil.copy2(path, backup_path)
        path.unlink()
        
        return f"✅ 文件 '{filepath}' 已删除\n📦 备份保存在: {backup_path}"
    
    except PermissionError:
        return f"错误：没有权限删除文件 '{filepath}'"
    except Exception as e:
        return f"错误：{str(e)}"

@tool
def move_file(source: str, destination: str) -> str:
    """移动/重命名文件 - 危险操作！"""
    try:
        src_path = Path(source)
        dst_path = Path(destination)
        
        if not src_path.exists():
            return f"错误：源文件 '{source}' 不存在"
        
        if dst_path.exists():
            return f"错误：目标 '{destination}' 已存在"
        
        # 创建目标目录（如果需要）
        dst_path.parent.mkdir(parents=True, exist_ok=True)
        
        shutil.move(str(src_path), str(dst_path))
        
        return f"✅ 文件已从 '{source}' 移动到 '{destination}'"
    
    except PermissionError:
        return f"错误：没有权限移动文件"
    except Exception as e:
        return f"错误：{str(e)}"

@tool
def create_directory(directory: str) -> str:
    """创建目录"""
    try:
        path = Path(directory)
        if path.exists():
            return f"错误：目录 '{directory}' 已存在"
        
        path.mkdir(parents=True, exist_ok=True)
        return f"✅ 目录 '{directory}' 创建成功"
    
    except PermissionError:
        return f"错误：没有权限创建目录 '{directory}'"
    except Exception as e:
        return f"错误：{str(e)}"

@tool
def human_approval(question: str) -> str:
    """请求人类确认危险操作"""
    return question

# ==================== 工具分类 ====================

# 安全工具（不需要确认）
SAFE_TOOLS = ["list_files", "read_file", "create_file", "create_directory"]

# 危险工具（需要人类确认）
DANGEROUS_TOOLS = ["delete_file", "move_file"]

# 所有工具
tools = [list_files, read_file, create_file, delete_file, move_file, create_directory, human_approval]
llm_with_tools = llm.bind_tools(tools)

# ==================== 代理节点定义 ====================

def agent_node(state: MessagesState):
    """代理思考节点"""
    print("🤖 Agent正在思考...")
    
    # 添加系统提示
    messages = state["messages"]
    if not any(isinstance(msg, SystemMessage) for msg in messages):
        system_msg = SystemMessage(content="""
你是一个文件管理助手。你可以帮助用户管理文件和目录。

可用的工具：
- list_files: 列出目录内容
- read_file: 读取文件内容  
- create_file: 创建新文件
- create_directory: 创建目录
- delete_file: 删除文件（危险操作，需要确认）
- move_file: 移动/重命名文件（危险操作，需要确认）

对于危险操作（删除、移动文件），你必须先调用 human_approval 工具请求用户确认，
确认问题应该清楚说明将要执行的操作和可能的风险。

请始终提供清晰、有用的响应。
""")
        messages = [system_msg] + messages
    
    response = llm_with_tools.invoke(messages)
    return {"messages": [response]}

def human_approval_node(state: MessagesState):
    """人类确认节点 - 实际上不会执行，因为会在此之前中断"""
    print("⏸️ 等待人类确认...")
    return {"messages": []}

# ==================== 路由逻辑 ====================

def router(state: MessagesState) -> Literal["tools", "__end__", "human_approval"]:
    """决定下一步的路由"""
    last_message = state["messages"][-1]
    
    if not last_message.tool_calls:
        return "__end__"
    
    tool_call = last_message.tool_calls[0]
    tool_name = tool_call.get("name")
    
    print(f"🔧 检测到工具调用: {tool_name}")
    
    if tool_name == "human_approval":
        return "human_approval"
    elif tool_name in DANGEROUS_TOOLS:
        # 对于危险工具，先请求确认
        print(f"⚠️ {tool_name} 是危险操作，需要人类确认")
        return "human_approval"
    else:
        print(f"✅ {tool_name} 是安全操作，直接执行")
        return "tools"

# ==================== 构建图 ====================

tool_node = ToolNode(tools)

graph_builder = StateGraph(MessagesState)
graph_builder.add_node("agent", agent_node)
graph_builder.add_node("tools", tool_node)
graph_builder.add_node("human_approval", human_approval_node)

graph_builder.set_entry_point("agent")

graph_builder.add_conditional_edges(
    "agent",
    router,
    {
        "tools": "tools",
        "human_approval": "human_approval",
        "__end__": END,
    }
)

graph_builder.add_edge("tools", "agent")
graph_builder.add_edge("human_approval", "agent")

# 编译图，设置中断点
memory = MemorySaver()
graph = graph_builder.compile(
    checkpointer=memory,
    interrupt_before=["human_approval"]
)

# ==================== 交互式运行函数 ====================

def run_interactive_agent():
    """运行交互式文件管理代理"""
    print("🎉 文件管理代理已启动！")
    print("💡 你可以要求我列出文件、读取文件、创建文件、删除文件等操作")
    print("⚠️ 危险操作（如删除文件）会要求你确认")
    print("📝 输入 'quit' 退出\n")
    
    config = {"configurable": {"thread_id": "file_agent_session"}}
    
    while True:
        try:
            user_input = input("👤 你: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            print(f"\n🚀 处理请求: {user_input}")
            print("-" * 50)
            
            # 开始处理请求
            events = list(graph.stream(
                {"messages": [HumanMessage(content=user_input)]}, 
                config
            ))
            
            # 显示结果
            for event in events:
                if "messages" in event:
                    messages = event["messages"]
                    if messages:
                        msg = messages[-1]
                        # 显示AI的回复
                        if hasattr(msg, 'content') and msg.content and not hasattr(msg, 'tool_calls'):
                            print(f"🤖 Agent: {msg.content}")
                        # 显示工具执行结果
                        elif hasattr(msg, 'content') and msg.content and str(type(msg).__name__) == 'ToolMessage':
                            print(f"🔧 工具结果: {msg.content}")
            
            # 检查是否需要人类确认
            state = graph.get_state(config)
            if state.next and "human_approval" in state.next:
                last_message = state.values["messages"][-1]
                if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                    tool_call = last_message.tool_calls[0]
                    tool_name = tool_call["name"]
                    tool_args = tool_call["args"]
                    
                    print(f"\n⚠️ 危险操作确认")
                    print(f"🔧 工具: {tool_name}")
                    print(f"📝 参数: {tool_args}")
                    
                    # 请求用户确认
                    while True:
                        confirm = input("\n❓ 是否继续执行？(yes/no): ").strip().lower()
                        if confirm in ['yes', 'y']:
                            # 继续执行
                            tool_call_id = tool_call["id"]
                            continue_events = list(graph.stream(
                                {"messages": [ToolMessage(
                                    content="用户已确认，继续执行", 
                                    tool_call_id=tool_call_id
                                )]},
                                config
                            ))
                            
                            for event in continue_events:
                                if "messages" in event:
                                    msg = event["messages"][-1]
                                    if hasattr(msg, 'content') and msg.content:
                                        print(f"🤖 Agent: {msg.content}")
                            break
                        elif confirm in ['no', 'n']:
                            print("❌ 操作已取消")
                            # 重置状态，取消当前操作
                            tool_call_id = tool_call["id"]
                            graph.stream(
                                {"messages": [ToolMessage(
                                    content="用户取消了操作", 
                                    tool_call_id=tool_call_id
                                )]},
                                config
                            )
                            break
                        else:
                            print("请输入 'yes' 或 'no'")
            
            print("\n" + "="*50 + "\n")
            
        except KeyboardInterrupt:
            print("\n👋 程序被中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            print("请重试或输入 'quit' 退出\n")

if __name__ == "__main__":
    run_interactive_agent()
