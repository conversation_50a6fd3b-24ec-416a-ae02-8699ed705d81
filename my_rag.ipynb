{"cells": [{"cell_type": "code", "execution_count": null, "id": "c23cd6d9", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "if not os.environ.get(\"GOOGLE_API_KEY\"):\n", "  os.environ[\"GOOGLE_API_KEY\"] = getpass.getpass(\"Enter API key for Google Gemini: \")\n", "\n", "from langchain_google_genai import GoogleGenerativeAIEmbeddings\n", "\n", "embeddings = GoogleGenerativeAIEmbeddings(model=\"models/embedding-001\")"]}, {"cell_type": "code", "execution_count": null, "id": "80aba7c0", "metadata": {}, "outputs": [], "source": ["from langchain_core.vectorstores import InMemoryVectorStore\n", "\n", "vector_store = InMemoryVectorStore(embeddings)"]}, {"cell_type": "code", "execution_count": null, "id": "c3c6e603", "metadata": {}, "outputs": [], "source": ["from langchain_milvus import Milvus\n", "\n", "URI = \"./milvus_example.db\"\n", "\n", "vector_store = Milvus(\n", "    embedding_function=embeddings,\n", "    connection_args={\"uri\": URI},\n", "    index_params={\"index_type\": \"FLAT\", \"metric_type\": \"L2\"},\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7a5b86e1", "metadata": {}, "outputs": [], "source": ["import bs4\n", "from langchain import hub\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain.core.documents import Document\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "\n", "\n", "loader = WebBaseLoader(\n", "    webpath=(\"https://lilianweng.github.io/posts/2023-06-23-agent/\",),\n", "    bs_kwargs=dict(\n", "        parse_only=bs4.SoupStrainer(\n", "            calss_=(\"post-content\", \"post-title\", \"post-header\")\n", "        )\n", "    )\n", ")\n", "\n", "docs = loader.load()\n", "\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=0)\n", "all_splitter = text_splitter.split_documents(docs)"]}, {"cell_type": "code", "execution_count": null, "id": "306f61c7", "metadata": {}, "outputs": [], "source": ["_ = vector_store.add_documents(all_splitter)"]}, {"cell_type": "code", "execution_count": null, "id": "8f54d741", "metadata": {}, "outputs": [], "source": ["from langchain "]}], "metadata": {"kernelspec": {"display_name": "my-langchain", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}