{"cells": [{"cell_type": "code", "execution_count": 27, "id": "d9c53c49", "metadata": {}, "outputs": [], "source": ["from typing import TypedDict, Annotated, List\n", "\n", "from langgraph.graph import StateGraph, START, END\n", "from langgraph.graph.message import add_messages\n", "\n", "# 第一步\n", "class State(TypedDict):\n", "    messages: Annotated[List, add_messages]\n", "    \n", "# builder是stategraph的实例，相当于乐高积木的底座\n", "graph_builder = StateGraph(State)"]}, {"cell_type": "code", "execution_count": 28, "id": "0c3d05b6", "metadata": {}, "outputs": [], "source": ["import os\n", "from langchain.chat_models import init_chat_model\n", "\n", "\n", "os.environ[\"GOOGLE_API_KEY\"] = 'AIzaSyCRMfVZuUDwLGH2gMDNYV5sawu5TbpMZSI'\n", "model = init_chat_model(\"gemini-2.0-flash\", model_provider=\"google_genai\")"]}, {"cell_type": "code", "execution_count": null, "id": "8c9b0db2", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.state.StateGraph at 0x7f4a204aa3e0>"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["def chatbot(state: State):\n", "    return {\"messages\": [model.invoke(state[\"messages\"])]}\n", "\n", "graph_builder.add_node(\"chatbot\", chatbot)"]}, {"cell_type": "code", "execution_count": 30, "id": "ae5d138f", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.state.StateGraph at 0x7f4a204aa3e0>"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["graph_builder.add_edge(START, \"chatbot\")"]}, {"cell_type": "code", "execution_count": 31, "id": "64b5cab4", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.state.StateGraph at 0x7f4a204aa3e0>"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["graph_builder.add_edge(\"chatbot\", END)"]}, {"cell_type": "code", "execution_count": 32, "id": "6cef92a4", "metadata": {}, "outputs": [], "source": ["graph = graph_builder.compile()"]}, {"cell_type": "code", "execution_count": 33, "id": "6a9688ea", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "try:\n", "    display(Image(graph.get_graph().draw_mermaid_png()))\n", "except Exception:\n", "    # This requires some extra dependencies and is optional\n", "    pass"]}, {"cell_type": "code", "execution_count": 34, "id": "53898b06", "metadata": {}, "outputs": [], "source": ["def stream_graph_updates(user_input: str):\n", "    for event in graph.stream({\"messages\": [{\"role\": \"user\", \"content\": user_input}]}):\n", "        for value in event.values():\n", "            print(\"Assistant:\", value[\"messages\"][-1].content)"]}, {"cell_type": "code", "execution_count": null, "id": "215ad6be", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎬 开始调试运行\n", "============================================================\n", "\n", "📍 步骤 1:\n", "   消息总数: 1\n", "   最新消息: HumanMessage\n", "   内容预览: 怎么做鱼香肉丝...\n", "\n", "📍 步骤 2:\n", "   消息总数: 2\n", "   最新消息: AIMessage\n", "   内容预览: 鱼香肉丝是一道经典的川菜，以其咸甜酸辣的独特风味而闻名。以下是制作鱼香肉丝的详细步骤和技巧：\n", "\n", "**一、准备食材：**\n", "\n", "*   **主料：**\n", "    *  ...\n"]}], "source": ["# from debug_toolkit import LangGraphDebugger\n", "# from langchain_core.messages import HumanMessage\n", "\n", "# debugger = LangGraphDebugger(graph)\n", "# debugger.debug_run(\n", "#     {\"messages\": [HumanMessage(content=\"怎么做鱼香肉丝\")]},\n", "#     {\"configurable\": {\"thread_id\": \"debug_session\"}}\n", "# )\n"]}, {"cell_type": "code", "execution_count": 43, "id": "aecb4454", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['InputType', 'OutputType', '__abstractmethods__', '__annotations__', '__class__', '__class_getitem__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__or__', '__orig_bases__', '__orig_class__', '__parameters__', '__reduce__', '__reduce_ex__', '__repr__', '__ror__', '__setattr__', '__sizeof__', '__slots__', '__str__', '__subclasshook__', '__weakref__', '_abatch_with_config', '_abc_impl', '_acall_with_config', '_aprepare_state_snapshot', '_atransform_stream_with_config', '_batch_with_config', '_call_with_config', '_defaults', '_is_protocol', '_migrate_checkpoint', '_prepare_state_snapshot', '_repr_mimebundle_', '_transform_stream_with_config', 'abatch', 'abatch_as_completed', 'abulk_update_state', 'aclear_cache', 'aget_graph', 'aget_state', 'aget_state_history', 'aget_subgraphs', 'ainvoke', 'as_tool', 'assign', 'astream', 'astream_events', 'astream_log', 'atransform', 'attach_branch', 'attach_edge', 'attach_node', 'aupdate_state', 'batch', 'batch_as_completed', 'bind', 'builder', 'bulk_update_state', 'cache', 'cache_policy', 'channels', 'checkpointer', 'clear_cache', 'config', 'config_schema', 'config_specs', 'config_type', 'copy', 'debug', 'get_config_jsonschema', 'get_graph', 'get_input_jsonschema', 'get_input_schema', 'get_name', 'get_output_jsonschema', 'get_output_schema', 'get_prompts', 'get_state', 'get_state_history', 'get_subgraphs', 'input_channels', 'input_schema', 'interrupt_after_nodes', 'interrupt_before_nodes', 'invoke', 'map', 'name', 'nodes', 'output_channels', 'output_schema', 'pick', 'pipe', 'retry_policy', 'schema_to_mapper', 'step_timeout', 'store', 'stream', 'stream_channels', 'stream_channels_asis', 'stream_channels_list', 'stream_eager', 'stream_mode', 'transform', 'trigger_to_nodes', 'update_state', 'validate', 'with_alisteners', 'with_config', 'with_fallbacks', 'with_listeners', 'with_retry', 'with_types']\n", "Assistant: \"Nich\" is a German word that translates to \"niche\" in English. It often refers to a specialized segment of the population or a specific area of interest.\n", "Goodbye!\n"]}], "source": ["while True:\n", "    try:\n", "        user_input = input(\"User: \")\n", "        \n", "        if user_input.lower() in [\"quit\", \"exit\", \"q\"]:\n", "            print(\"Goodbye!\")\n", "            break\n", "        print(dir(graph))\n", "        stream_graph_updates(user_input)\n", "    except:\n", "        # fallback if input() is not available\n", "        user_input = \"What do you know about LangGraph?\"\n", "        print(\"User: \" + user_input)\n", "        stream_graph_updates(user_input)\n", "        break\n", "    \n"]}], "metadata": {"kernelspec": {"display_name": "my-langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}