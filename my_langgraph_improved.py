from typing import TypedDict, Annotated, List
from langchain_core.messages import BaseMessage, HumanMessage
from langgraph.graph.message import add_messages
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolNode
from langgraph.checkpoint.memory import MemorySaver
from tools import tools, DANGEROUS_TOOLS
from lchat import model

class AgentState(TypedDict):
    messages: Annotated[List[BaseMessage], add_messages]

# 绑定工具到模型
model_with_tools = model.bind_tools(tools)

def call_model(state: AgentState):
    response = model_with_tools.invoke(state['messages'])
    return {"messages": [response]}

# 添加调试信息
def should_continue(state: AgentState):
    last_message = state['messages'][-1]
    
    if not last_message.tool_calls:
        return "end"
    
    tool_name = last_message.tool_calls[0]['name']
    print(f"🔍 检测到工具调用: {tool_name}")
    print(f"🔍 危险工具列表: {DANGEROUS_TOOLS}")
    
    if tool_name in DANGEROUS_TOOLS:
        print(f"⚠️ {tool_name} 是危险工具，将被拦截")
        return "dangerous_tools"
    else:
        print(f"✅ {tool_name} 是安全工具，直接执行")
        return "safe_tools"

# 创建工具节点
safe_tool_node = ToolNode(tools)
dangerous_tool_node = ToolNode(tools)

# 构建图
workflow = StateGraph(AgentState)
workflow.add_node("agent", call_model)
workflow.add_node("safe_tools", safe_tool_node)
workflow.add_node("dangerous_tools", dangerous_tool_node)

workflow.set_entry_point("agent")
workflow.add_conditional_edges("agent", should_continue, {
    "safe_tools": "safe_tools",
    "dangerous_tools": "dangerous_tools",
    "end": END
})
workflow.add_edge("safe_tools", "agent")
workflow.add_edge("dangerous_tools", "agent")

# 编译时设置中断点
memory = MemorySaver()
app = workflow.compile(
    checkpointer=memory,
    interrupt_before=["dangerous_tools"]
)

import uuid
from langchain_core.messages import HumanMessage

def run_agent_with_approval():
    """运行带人工审批的 Agent"""
    config = {"configurable": {"thread_id": str(uuid.uuid4())}}
    
    print("🤖 LangGraph Agent 启动！")
    print("=" * 50)
    
    while True:
        try:
            # 获取用户输入
            user_input = input("\n👤 请输入您的问题 (输入 'quit' 退出): ")
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            
            # 跳过空输入
            if not user_input.strip():
                print("⚠️ 请输入有效的问题")
                continue
            
            print(f"\n🔄 处理中: {user_input}")
            print("-" * 30)
            
            # 尝试执行
            result = app.invoke(
                {"messages": [HumanMessage(content=user_input)]}, 
                config
            )
            
            # 显示结果
            print("\n✅ 执行完成:")
            if result and "messages" in result:
                result["messages"][-1].pretty_print()
            
        except Exception as e:
            # 检查是否是中断（需要人工批准）
            if "interrupt" in str(e).lower() or "Interrupted" in str(e):
                print("\n⚠️  检测到危险工具调用！")
                
                # 获取当前状态，显示工具调用信息
                current_state = app.get_state(config)
                if current_state.values and "messages" in current_state.values:
                    last_message = current_state.values["messages"][-1]
                    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                        tool_call = last_message.tool_calls[0]
                        print(f"🔧 工具名称: {tool_call['name']}")
                        print(f"📝 参数: {tool_call['args']}")
                
                # 询问用户批准
                approval = input("\n❓ 是否允许执行此危险操作？(yes/no): ").lower().strip()
                
                if approval in ['yes', 'y']:
                    print("\n✅ 用户已批准，继续执行...")
                    try:
                        # 继续执行
                        result = app.invoke(None, config)
                        print("\n🎉 执行完成:")
                        if result and "messages" in result:
                            result["messages"][-1].pretty_print()
                    except Exception as continue_error:
                        print(f"❌ 执行出错: {continue_error}")
                else:
                    print("🚫 用户拒绝执行，操作已取消")
            else:
                print(f"❌ 发生错误: {e}")
                # 重置会话以避免状态污染
                config = {"configurable": {"thread_id": str(uuid.uuid4())}}
                print("🔄 已重置会话状态")

def demo_run():
    """演示运行"""
    config = {"configurable": {"thread_id": "demo_session"}}
    
    print("🎬 演示模式")
    print("=" * 50)
    
    # 测试安全工具
    print("\n1️⃣ 测试安全工具:")
    result1 = app.invoke(
        {"messages": [HumanMessage(content="List the files in my project directory.")]}, 
        config
    )
    result1["messages"][-1].pretty_print()
    
    print("\n" + "=" * 50)
    
    # 测试危险工具
    print("\n2️⃣ 测试危险工具:")
    try:
        result2 = app.invoke(
            {"messages": [HumanMessage(content="Please remove the file 'files.txt'")]}, 
            config
        )
    except Exception as e:
        if "interrupt" in str(e).lower():
            print("⚠️  危险工具被拦截，需要人工批准")
            
            # 模拟批准
            print("✅ 模拟用户批准...")
            result2 = app.invoke(None, config)
            result2["messages"][-1].pretty_print()

if __name__ == "__main__":
    print("选择运行模式:")
    print("1. 交互模式")
    print("2. 演示模式")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "1":
        run_agent_with_approval()
    elif choice == "2":
        demo_run()
    else:
        print("无效选择，启动交互模式...")
        run_agent_with_approval()