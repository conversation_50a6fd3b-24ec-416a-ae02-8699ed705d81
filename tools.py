from langchain_core.tools import tool
import os

@tool
def list_files(directory: str) -> str:
    """Lists files in a given directory."""
    # files = os.listdir(directory)
    return f"files.txt, images/, app.py"

@tool
def remove_file(filepath: str) -> str:
    """Removes a file. CRITICAL: This is a destructive action and cannot be undone."""
    # In a real scenario, this would be: os.remove(filepath)
    print(f"--- EXECUTING RM on '{filepath}' ---")
    return f"Successfully removed {filepath}"

# 分类工具
tools = [list_files, remove_file]
DANGEROUS_TOOLS = ["remove_file"]