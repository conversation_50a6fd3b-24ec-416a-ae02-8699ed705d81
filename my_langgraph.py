from typing import TypedDict, Annotated, List
from langchain_core.messages import BaseMessage
from langgraph.graph.message import add_messages

class AgentState(TypedDict):
    messages: Annotated[List[BaseMessage], add_messages]
    
from langgraph.prebuilt import ToolNode

from tools import tools, DANGEROUS_TOOLS

from lchat import model

# 定义节点
# 1. 模型思考节点
def call_model(state: AgentState):
    messages = state['messages']
    response = model.invoke(messages) # model 是你绑定的带工具的LLM
    return {"messages": [response]}

# 2. 工具执行节点 (用于安全工具)
# ToolNode 是一个预构建的节点，非常方便
safe_tool_node = ToolNode(tools)

# 3. 人类批准节点 - 这个节点本身不做任何事！
# 它的存在就是作为一个“暂停点”，让图的执行停在这里。
# 我们通过返回一个特定的消息来清晰地表明当前状态。
from langchain_core.messages import AIMessage, HumanMessage

def stop_and_ask_human(state: AgentState):
    # 获取AI的最后一个意图（即调用危险工具的请求）
    ai_message = state['messages'][-1]
    tool_call = ai_message.tool_calls[0]
    tool_name = tool_call['name']
    tool_args = tool_call['args']
    
    # 创建一个文本，用于向用户展示
    prompt_text = (
        f"AI wants to run the DANGEROUS tool '{tool_name}' with arguments {tool_args}. "
        "Please reply with 'yes' to approve or 'no' to deny."
    )
    
    # 返回这个提示，应用程序会把它显示给用户
    # 注意：我们没有改变原有的消息列表，只是准备了一个提示
    # 或者，你可以在状态中增加一个专门的字段来存放这个提示
    # 为了简单，我们让调用者处理这个暂停状态
    return {} # 返回空，因为我们不想自动添加任何消息


from langchain_core.messages import AIMessage

def should_continue(state: AgentState):
    last_message = state['messages'][-1]

    # 1. 如果没有工具调用，结束流程
    if not last_message.tool_calls:
        return "end"

    # 2. 如果有工具调用，检查是否危险
    tool_name = last_message.tool_calls[0]['name']
    if tool_name in DANGEROUS_TOOLS:
        return "ask_human"  # 走向“请求批准”的节点
    else:
        return "continue"   # 走向“自动执行”的节点

# 我们还需要一个函数来处理人类的反馈
def after_human_approval(state: AgentState):
    last_message = state['messages'][-1]
    
    # 假设人类的回复是最后一条消息
    if isinstance(last_message, HumanMessage):
        if "yes" in last_message.content.lower():
            # 如果批准了，需要把之前AI的工具调用请求重新包装起来
            # 让 ToolNode 可以执行
            ai_message_with_tool_call = None
            for msg in reversed(state['messages'][:-1]):
                if isinstance(msg, AIMessage) and msg.tool_calls:
                    ai_message_with_tool_call = msg
                    break
            
            # 这里需要一些状态操作来确保 ToolNode 能正确执行
            # 一个更健壮的方法是在 state 中传递 tool_call 信息
            # 为了简化，我们直接返回执行危险工具
            return "execute_dangerous_tool"
        else:
            # 如果拒绝了，让AI重新思考
            return "replan"
    return "end"
from langgraph.graph import StateGraph, END

# 创建图
workflow = StateGraph(AgentState)

# 添加节点
workflow.add_node("agent", call_model)
workflow.add_node("safe_tools", safe_tool_node)
workflow.add_node("human_approval_node", stop_and_ask_human)
# 危险工具也用同一个 ToolNode，因为它的输入格式是一样的
workflow.add_node("dangerous_tools", safe_tool_node) 

# 设置入口点
workflow.set_entry_point("agent")

# 添加条件性的边
workflow.add_conditional_edges(
    "agent",  # 从 agent 节点出发
    should_continue, # 使用我们的判断函数
    {
        "continue": "safe_tools",     # 如果返回 "continue"，去 safe_tools
        "ask_human": "human_approval_node", # 如果返回 "ask_human"，去暂停节点
        "end": END                    # 如果返回 "end"，结束
    }
)

# 添加从工具执行返回到 agent 的边
workflow.add_edge("safe_tools", "agent")

#
# 关键：从暂停节点出发的边。这个边不会自动触发。
#
workflow.add_conditional_edges(
    "human_approval_node",
    after_human_approval,
    {
        "execute_dangerous_tool": "dangerous_tools",
        "replan": "agent", # 把包含 "no" 的消息传给agent，让它重新规划
        "end": END
    }
)

# 危险工具执行完后，也返回给 agent
workflow.add_edge("dangerous_tools", "agent")

# 编译图
app = workflow.compile()
import uuid
from langchain_core.messages import HumanMessage

# 使用唯一的 thread_id 来维持对话状态
config = {"configurable": {"thread_id": str(uuid.uuid4())}}
messages = [HumanMessage(content="List the files in my project directory.")]

# 第一次调用（安全工具）
# stream() 会返回每个步骤之后的状态
for event in app.stream({"messages": messages}, config=config, stream_mode="values"):
    # stream_mode="values" 让我们可以方便地获取 state
    event["messages"][-1].pretty_print()

print("\n" + "="*40 + "\n")

# 第二次调用（危险工具）
messages = [HumanMessage(content="Great. Now please remove the file 'files.txt'")]
human_approval_needed = False

for event in app.stream({"messages": messages}, config=config, stream_mode="values"):
    event["messages"][-1].pretty_print()
    # 检查图是否到达了我们的暂停节点
    # 注意：更可靠的方法是检查下一个步骤是什么，而不是检查消息内容
    if "__end__" not in event and list(event.keys())[-1] == "human_approval_node":
         human_approval_needed = True
         break # 暂停我们的循环

# 如果需要批准，则与用户交互
if human_approval_needed:
    print("\n--- HUMAN INTERVENTION REQUIRED ---")
    user_input = input("AI wants to run a dangerous tool. Approve? (yes/no): ")

    # 把用户的决定作为一条新消息，继续执行图
    if user_input.lower() == "yes":
        print("--- USER APPROVED. RESUMING GRAPH ---")
        # 恢复执行
        for event in app.stream({"messages": [HumanMessage(content="yes")]}, config=config, stream_mode="values"):
             event["messages"][-1].pretty_print()
    else:
        print("--- USER DENIED. RESUMING GRAPH FOR REPLANNING ---")
        for event in app.stream({"messages": [HumanMessage(content="no")]}, config=config, stream_mode="values"):
             event["messages"][-1].pretty_print()