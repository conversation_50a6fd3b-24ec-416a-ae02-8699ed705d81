# 真实文件操作代理 (Real File Agent)

这是一个基于LangGraph和Google Gemini的真实文件操作代理，支持人机协作的安全文件管理。

## 功能特性

### 🔧 文件操作工具

1. **list_files(directory)** - 列出目录内容
   - 显示文件和文件夹
   - 支持相对和绝对路径
   - 安全操作，无需确认

2. **read_file(filepath)** - 读取文件内容
   - 支持UTF-8编码的文本文件
   - 显示完整文件内容
   - 安全操作，无需确认

3. **create_file(filepath, content)** - 创建新文件
   - 自动创建父目录
   - 防止覆盖已存在文件
   - 安全操作，无需确认

4. **delete_file(filepath)** - 删除文件 ⚠️
   - **危险操作，需要人类确认**
   - 自动创建备份文件
   - 备份保存在 `./backups/` 目录

5. **create_directory(directory)** - 创建目录
   - 支持递归创建父目录
   - 安全操作，无需确认

### 🛡️ 安全机制

#### 人机协作确认流程
1. 用户请求危险操作（如删除文件）
2. 代理自动调用 `human_approval` 工具
3. 系统暂停执行，等待人类确认
4. 用户可以选择：
   - `yes` - 继续执行操作
   - `no` - 取消操作

#### 备份机制
- 删除文件前自动创建备份
- 备份文件命名格式：`原文件名_时间戳.backup`
- 备份保存在 `./backups/` 目录

## 使用示例

### 启动代理
```bash
python simple_file_agent.py
```

### 基本操作示例

```
👤 你: list files in current directory
🤖 Agent: 目录 '.' 内容:
📁 backups/
📄 another.txt
📄 hello.txt
...

👤 你: create a file called test.txt with content "Hello World"
🤖 Agent: ✅ 文件 'test.txt' 创建成功

👤 你: read test.txt
🤖 Agent: 文件 'test.txt' 内容:
Hello World

👤 你: delete test.txt
⚠️ 确认危险操作:
工具: human_approval
参数: {'question': '确认删除文件 test.txt？'}
继续执行？(yes/no): yes
🤖 Agent: ✅ 文件 'test.txt' 已删除，备份: backups/test.txt_20250727_190353.backup
```

## 技术架构

### 核心组件

1. **LangGraph状态图**
   - `agent` 节点：AI思考和决策
   - `tools` 节点：执行安全工具
   - `human_approval` 节点：人类确认点

2. **路由逻辑**
   - 自动识别工具类型
   - 危险操作触发确认流程
   - 安全操作直接执行

3. **中断机制**
   - 在 `human_approval` 节点前设置中断
   - 支持用户交互确认
   - 可恢复执行或取消操作

### 工具分类

```python
# 安全工具（无需确认）
SAFE_TOOLS = ["list_files", "read_file", "create_file", "create_directory"]

# 危险工具（需要确认）
DANGEROUS_TOOLS = ["delete_file"]
```

## 安全特性

### ✅ 已实现的安全措施

1. **操作前确认** - 危险操作需要明确的人类确认
2. **自动备份** - 删除文件前自动创建备份
3. **权限检查** - 处理权限错误和异常情况
4. **路径验证** - 防止无效路径操作
5. **错误处理** - 友好的错误信息提示

### 🔒 推荐的额外安全措施

1. **路径限制** - 限制操作范围到特定目录
2. **文件类型过滤** - 限制可操作的文件类型
3. **操作日志** - 记录所有文件操作历史
4. **回滚功能** - 支持撤销最近的操作

## 扩展功能

可以轻松添加更多文件操作工具：

```python
@tool
def move_file(source: str, destination: str) -> str:
    """移动/重命名文件 - 危险操作"""
    # 实现移动逻辑
    pass

@tool
def copy_file(source: str, destination: str) -> str:
    """复制文件 - 安全操作"""
    # 实现复制逻辑
    pass
```

## 依赖项

- `langgraph` - 状态图框架
- `langchain` - LLM集成
- `google-generativeai` - Google Gemini API

## 配置

在使用前需要设置Google API密钥：

```python
os.environ["GOOGLE_API_KEY"] = "your-api-key-here"
```

## 总结

这个文件操作代理展示了如何构建一个安全的、人机协作的AI系统：

1. **智能决策** - AI能够理解用户意图并选择合适的工具
2. **安全控制** - 危险操作需要人类明确确认
3. **用户友好** - 清晰的交互界面和错误提示
4. **可扩展性** - 易于添加新的文件操作功能
5. **容错性** - 完善的错误处理和恢复机制

这种设计模式可以应用到其他需要人机协作的AI系统中，确保AI在执行关键操作时始终有人类监督。
