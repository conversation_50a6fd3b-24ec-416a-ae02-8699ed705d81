{"cells": [{"cell_type": "code", "execution_count": 1, "id": "93c4f2c6", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'query': \"What's a 'node' in LangGraph?\",\n", " 'follow_up_questions': None,\n", " 'answer': None,\n", " 'images': [],\n", " 'results': [{'url': 'https://medium.com/@vivekvjnk/langgraph-basics-understanding-state-schema-nodes-and-edges-77f2fd17cae5',\n", "   'title': 'LangGraph Basics: Understanding State, Schema, Nodes, and Edges',\n", "   'content': 'Nodes: Perform the actual work. Nodes contain Python code that can execute any logic, from simple computations to LLM calls or integrations.',\n", "   'score': 0.79254496,\n", "   'raw_content': None},\n", "  {'url': 'https://blog.langchain.com/langgraph/',\n", "   'title': 'LangGraph - <PERSON><PERSON><PERSON><PERSON> Blog',\n", "   'content': \"TL;DR: LangGraph is module built on top of LangChain to better enable creation of cyclical graphs, often needed for agent runtimes. This state is updated by nodes in the graph, which return operations to attributes of this state (in the form of a key-value store). After adding nodes, you can then add edges to create the graph. An example of this may be in the basic agent runtime, where we always want the model to be called after we call a tool. The state of this graph by default contains concepts that should be familiar to you if you've used LangChain agents: `input`, `chat_history`, `intermediate_steps` (and `agent_outcome` to represent the most recent agent outcome)\",\n", "   'score': 0.7407191,\n", "   'raw_content': None}],\n", " 'response_time': 2.1}"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_tavily import TavilySearch\n", "\n", "tool = TavilySearch(max_results=2)\n", "tools = [tool]\n", "tool.invoke(\"What's a 'node' in LangGraph?\")"]}, {"cell_type": "code", "execution_count": null, "id": "90c00651", "metadata": {}, "outputs": [], "source": ["from typing import Annotated\n", "\n", "from typing_extensions import TypedDict\n", "\n", "from langgraph.graph import StateGraph, START, END\n", "from langgraph.graph.message import add_messages\n", "\n", "class State(TypedDict):\n", "    messages: Annotated[list, add_messages]\n", "\n", "graph_builder = StateGraph(State)\n", "\n", "# Modification: tell the LLM which tools it can call\n", "# highlight-next-line\n", "llm_with_tools = llm.bind_tools(tools)\n", "\n", "def chatbot(state: State):\n", "    return {\"messages\": [llm_with_tools.invoke(state[\"messages\"])]}\n", "\n", "graph_builder.add_node(\"chatbot\", chatbot)"]}], "metadata": {"kernelspec": {"display_name": "my-langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}