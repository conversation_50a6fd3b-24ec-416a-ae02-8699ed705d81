{"cells": [{"cell_type": "code", "execution_count": 4, "id": "811c31a3", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "try:\n", "    # load environment variables from .env file (requires `python-dotenv`)\n", "    from dotenv import load_dotenv\n", "\n", "    load_dotenv()\n", "except ImportError:\n", "    pass\n", "\n", "os.environ[\"LANGSMITH_TRACING\"] = \"true\"\n", "if \"LANGSMITH_API_KEY\" not in os.environ:\n", "    os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass(\n", "        prompt=\"Enter your LangSmith API key (optional): \"\n", "    )\n", "if \"LANGSMITH_PROJECT\" not in os.environ:\n", "    os.environ[\"LANGSMITH_PROJECT\"] = getpass.getpass(\n", "        prompt='Enter your LangSmith Project Name (default = \"default\"): '\n", "    )\n", "    if not os.environ.get(\"LANGSMITH_PROJECT\"):\n", "        os.environ[\"LANGSMITH_PROJECT\"] = \"default\""]}, {"cell_type": "code", "execution_count": 5, "id": "570f8d5a", "metadata": {}, "outputs": [], "source": ["if not os.environ.get(\"GOOGLE_API_KEY\"):\n", "  os.environ[\"GOOGLE_API_KEY\"] = getpass.getpass(\"Enter API key for Google Gemini: \")\n", "\n", "from langchain.chat_models import init_chat_model\n", "\n", "model = init_chat_model(\"gemini-2.0-flash\", model_provider=\"google_genai\")"]}, {"cell_type": "code", "execution_count": 6, "id": "dd822353", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Ciao!', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--8b5179f1-6bde-475e-86e5-e43604f7de7a-0', usage_metadata={'input_tokens': 9, 'output_tokens': 3, 'total_tokens': 12, 'input_token_details': {'cache_read': 0}})"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.messages import HumanMessage, SystemMessage\n", "\n", "messages = [\n", "    SystemMessage(\"Translate the following from English into Italian\"),\n", "    HumanMessage(\"hi!\"),\n", "]\n", "\n", "model.invoke(messages)"]}, {"cell_type": "code", "execution_count": 7, "id": "d0e1fb93", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hi there! How can I help you today?\n"]}], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a helpful assistant\"),\n", "    (\"placeholder\", \"{chat_history}\"),\n", "    (\"human\", \"{input}\"),\n", "    (\"placeholder\", \"{agent_scratchpad}\")\n", "])\n", "\n", "chain = prompt | model | StrOutputParser()\n", "result = chain.invoke({\"input\": \"hi!\"})\n", "print(result)"]}, {"cell_type": "code", "execution_count": 11, "id": "c6f28b3d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'output': 'Which city are you interested in?\\n', 'messages': [AIMessage(content='Which city are you interested in?\\n', additional_kwargs={}, response_metadata={})]}\n"]}], "source": ["from langchain.agents import create_tool_calling_agent, AgentExecutor\n", "from langchain_core.tools import tool\n", "\n", "@tool\n", "def get_weather(city: str) -> str:\n", "    \"\"\"Get weather for a city\"\"\"\n", "    return f\"Weather in {city}: Sunny, 23°C\"\n", "\n", "tools = [get_weather]\n", "agent = create_tool_calling_agent(model, tools, prompt)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools)\n", "\n", "result = agent_executor.invoke({\"input\": \"What about Tokyo?\"})\n", "for chunk in agent_executor.stream({\"input\": \"Tell me about the weather\"}):\n", "    print(chunk)"]}, {"cell_type": "code", "execution_count": 14, "id": "f3fa0f7b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'messages': [HumanMessage(content='Weather in SF?', additional_kwargs={}, response_metadata={}, id='e3f60006-b89a-4eaa-ad86-d14a8650aff8'), AIMessage(content='', additional_kwargs={'function_call': {'name': 'get_weather', 'arguments': '{\"city\": \"SF\"}'}}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--4ffb53ff-23bd-4fb7-859e-779c2b1d1a8b-0', tool_calls=[{'name': 'get_weather', 'args': {'city': 'SF'}, 'id': '010dbabb-83e5-4493-be6f-a2b923db9e12', 'type': 'tool_call'}], usage_metadata={'input_tokens': 16, 'output_tokens': 5, 'total_tokens': 21, 'input_token_details': {'cache_read': 0}}), ToolMessage(content='Weather in SF: Sunny, 23°C', name='get_weather', id='c0c29d92-9968-4056-a1d7-5163c38778f2', tool_call_id='010dbabb-83e5-4493-be6f-a2b923db9e12'), AIMessage(content='OK. The weather in SF is Sunny, 23°C.', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--d7806d51-8811-48f3-8f08-02836714a590-0', usage_metadata={'input_tokens': 36, 'output_tokens': 16, 'total_tokens': 52, 'input_token_details': {'cache_read': 0}}), HumanMessage(content='What about Tokyo?', additional_kwargs={}, response_metadata={}, id='8a156804-0d20-4f1b-b5e1-2ab89ebff263'), AIMessage(content='', additional_kwargs={'function_call': {'name': 'get_weather', 'arguments': '{\"city\": \"Tokyo\"}'}}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--d613d91f-18b3-4094-87d2-8c7fcc2379b6-0', tool_calls=[{'name': 'get_weather', 'args': {'city': 'Tokyo'}, 'id': 'ec7f087e-f5b2-4415-a5ad-84105cf0b5d0', 'type': 'tool_call'}], usage_metadata={'input_tokens': 55, 'output_tokens': 5, 'total_tokens': 60, 'input_token_details': {'cache_read': 0}}), ToolMessage(content='Weather in Tokyo: Sunny, 23°C', name='get_weather', id='6cc9ed86-361f-4961-b929-98b43d07f4f1', tool_call_id='ec7f087e-f5b2-4415-a5ad-84105cf0b5d0'), AIMessage(content='OK. The weather in Tokyo is Sunny, 23°C.', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--805156fb-00c5-4e1e-9251-d6e0839b3771-0', usage_metadata={'input_tokens': 75, 'output_tokens': 16, 'total_tokens': 91, 'input_token_details': {'cache_read': 0}})]}\n"]}], "source": ["from langgraph.prebuilt import create_react_agent\n", "from langgraph.checkpoint.memory import MemorySaver\n", "\n", "memory = MemorySaver()\n", "app = create_react_agent(model, tools, checkpointer=memory)\n", "config = {\"configurable\": {\"thread_id\": \"user_123\"}}\n", "response1 = app.invoke({\"messages\": [(\"user\", \"Weather in SF?\")]}, config)\n", "response2 = app.invoke({\"messages\": [(\"user\", \"What about Tokyo?\")]}, config)\n", "\n", "print(response2)"]}], "metadata": {"kernelspec": {"display_name": "my-langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}