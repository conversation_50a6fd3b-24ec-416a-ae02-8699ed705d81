from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse, HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import json
import uuid
from langchain_core.messages import HumanMessage

# 导入你的 LangGraph 应用
from my_langgraph_improved import app as langgraph_app

# 创建 FastAPI 应用
app = FastAPI(title="LangGraph Chat API", version="1.0.0")

# 添加 CORS 支持
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ChatRequest(BaseModel):
    message: str
    thread_id: Optional[str] = None  # 如果不提供，自动生成

class ChatResponse(BaseModel):
    response: str
    thread_id: str

# 🔍 关键的流式格式化器
async def stream_formatter(request: ChatRequest):
    """
    异步生成器：将 LangGraph 的复杂输出转换为前端友好的流式数据
    """
    # 准备配置
    thread_id = request.thread_id or str(uuid.uuid4())
    config = {"configurable": {"thread_id": thread_id}}
    inputs = {"messages": [HumanMessage(content=request.message)]}
    
    try:
        # 发送开始信号
        yield f"data: {json.dumps({'type': 'start', 'thread_id': thread_id})}\n\n"
        
        # 异步迭代 LangGraph 的流
        async for chunk in langgraph_app.astream(inputs, config=config):
            # 处理不同类型的节点输出
            for node_name, node_data in chunk.items():
                if node_name == "agent" and "messages" in node_data:
                    messages = node_data["messages"]
                    if messages and hasattr(messages[-1], 'content'):
                        content = messages[-1].content
                        if content:  # 只发送非空内容
                            yield f"data: {json.dumps({'type': 'message', 'content': content, 'node': node_name})}\n\n"
                
                elif node_name in ["safe_tools", "dangerous_tools"] and "messages" in node_data:
                    # 工具执行结果
                    messages = node_data["messages"]
                    if messages:
                        tool_result = str(messages[-1]) if messages[-1] else "工具执行完成"
                        yield f"data: {json.dumps({'type': 'tool_result', 'content': tool_result, 'node': node_name})}\n\n"
                
                # 发送节点状态更新
                yield f"data: {json.dumps({'type': 'node_update', 'node': node_name, 'status': 'completed'})}\n\n"
        
        # 发送完成信号
        yield f"data: {json.dumps({'type': 'end', 'thread_id': thread_id})}\n\n"
        
    except Exception as e:
        # 发送错误信息
        error_msg = f"执行出错: {str(e)}"
        yield f"data: {json.dumps({'type': 'error', 'content': error_msg})}\n\n"

@app.post("/chat/stream")
async def chat_stream(request: ChatRequest):
    """
    流式聊天端点 - 实时返回 LangGraph 执行过程
    """
    if not request.message.strip():
        raise HTTPException(status_code=400, detail="消息不能为空")
    
    return StreamingResponse(
        stream_formatter(request),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
        }
    )

@app.post("/chat", response_model=ChatResponse)
async def chat_simple(request: ChatRequest):
    """
    简单聊天端点 - 返回完整结果
    """
    if not request.message.strip():
        raise HTTPException(status_code=400, detail="消息不能为空")
    
    thread_id = request.thread_id or str(uuid.uuid4())
    config = {"configurable": {"thread_id": thread_id}}
    inputs = {"messages": [HumanMessage(content=request.message)]}
    
    try:
        # 调用 LangGraph 应用
        result = await langgraph_app.ainvoke(inputs, config=config)
        
        # 提取最后的响应
        if result and "messages" in result and result["messages"]:
            last_message = result["messages"][-1]
            response_content = getattr(last_message, 'content', str(last_message))
        else:
            response_content = "抱歉，我没有生成有效的响应。"
        
        return ChatResponse(response=response_content, thread_id=thread_id)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理请求时出错: {str(e)}")

# 🎯 处理危险工具的特殊端点
@app.post("/chat/approve")
async def approve_dangerous_action(thread_id: str, approved: bool = True):
    """
    批准或拒绝危险操作
    """
    config = {"configurable": {"thread_id": thread_id}}
    
    try:
        if approved:
            # 继续执行被中断的操作
            result = await langgraph_app.ainvoke(None, config=config)
            return {"status": "approved", "result": "操作已执行"}
        else:
            # 拒绝操作，可能需要重新开始对话
            return {"status": "denied", "result": "操作已取消"}
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理批准请求时出错: {str(e)}")

@app.get("/")
async def get_chat_interface():
    """
    简单的聊天界面用于测试
    """
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>LangGraph Chat</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            #messages { height: 400px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; margin: 10px 0; }
            .message { margin: 10px 0; padding: 5px; }
            .user { background-color: #e3f2fd; }
            .assistant { background-color: #f3e5f5; }
            .tool { background-color: #fff3e0; }
            .error { background-color: #ffebee; color: red; }
            input[type="text"] { width: 70%; padding: 10px; }
            button { padding: 10px 20px; margin: 5px; }
        </style>
    </head>
    <body>
        <h1>🤖 LangGraph Chat Interface</h1>
        <div id="messages"></div>
        <div>
            <input type="text" id="messageInput" placeholder="输入您的消息..." />
            <button onclick="sendMessage()">发送</button>
            <button onclick="sendStreamMessage()">流式发送</button>
            <button onclick="clearMessages()">清空</button>
        </div>
        <div>
            <label>Thread ID: </label>
            <input type="text" id="threadId" placeholder="留空自动生成" />
        </div>

        <script>
            let currentThreadId = '';
            
            function addMessage(content, type = 'assistant') {
                const messages = document.getElementById('messages');
                const div = document.createElement('div');
                div.className = `message ${type}`;
                div.innerHTML = `<strong>${type}:</strong> ${content}`;
                messages.appendChild(div);
                messages.scrollTop = messages.scrollHeight;
            }
            
            async function sendMessage() {
                const input = document.getElementById('messageInput');
                const threadId = document.getElementById('threadId').value || currentThreadId;
                const message = input.value.trim();
                
                if (!message) return;
                
                addMessage(message, 'user');
                input.value = '';
                
                try {
                    const response = await fetch('/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ message, thread_id: threadId })
                    });
                    
                    const data = await response.json();
                    currentThreadId = data.thread_id;
                    document.getElementById('threadId').value = currentThreadId;
                    addMessage(data.response, 'assistant');
                    
                } catch (error) {
                    addMessage(`错误: ${error.message}`, 'error');
                }
            }
            
            async function sendStreamMessage() {
                const input = document.getElementById('messageInput');
                const threadId = document.getElementById('threadId').value || currentThreadId;
                const message = input.value.trim();
                
                if (!message) return;
                
                addMessage(message, 'user');
                input.value = '';
                
                try {
                    const response = await fetch('/chat/stream', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ message, thread_id: threadId })
                    });
                    
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    
                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;
                        
                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\\n');
                        
                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.slice(6));
                                    
                                    if (data.type === 'start') {
                                        currentThreadId = data.thread_id;
                                        document.getElementById('threadId').value = currentThreadId;
                                    } else if (data.type === 'message') {
                                        addMessage(data.content, 'assistant');
                                    } else if (data.type === 'tool_result') {
                                        addMessage(data.content, 'tool');
                                    } else if (data.type === 'error') {
                                        addMessage(data.content, 'error');
                                    }
                                } catch (e) {
                                    console.log('解析数据出错:', e);
                                }
                            }
                        }
                    }
                } catch (error) {
                    addMessage(`流式请求错误: ${error.message}`, 'error');
                }
            }
            
            function clearMessages() {
                document.getElementById('messages').innerHTML = '';
            }
            
            // 回车发送
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

# 健康检查端点
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "LangGraph Chat API"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)