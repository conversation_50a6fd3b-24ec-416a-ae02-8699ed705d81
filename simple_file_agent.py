#!/usr/bin/env python3
"""
简化版真实文件操作代理
"""

import os
import shutil
from pathlib import Path
from typing import Literal
from datetime import datetime

# 配置工作目录 - 可以通过环境变量覆盖
WORK_DIR = Path(os.getenv('FILE_AGENT_ROOT', '.')).absolute()
print(f"🏠 文件代理工作目录: {WORK_DIR}")

def get_safe_path(directory: str) -> tuple[Path, str]:
    """获取安全的路径，确保不会访问工作目录外的内容"""
    try:
        if directory == "." or directory == "":
            path = WORK_DIR
        elif not os.path.isabs(directory):
            path = WORK_DIR / directory
        else:
            path = Path(directory)

        # 解析路径并检查是否在工作目录内
        resolved_path = path.resolve()
        work_dir_resolved = WORK_DIR.resolve()

        if not str(resolved_path).startswith(str(work_dir_resolved)):
            return None, f"错误：不允许访问工作目录外的路径 '{directory}'"

        return resolved_path, ""
    except Exception as e:
        return None, f"路径解析错误: {str(e)}"

from langgraph.graph import StateGraph, END, MessagesState
from langgraph.prebuilt import ToolNode
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.messages import SystemMessage, ToolMessage, HumanMessage
from langchain_core.tools import tool
from langchain.chat_models import init_chat_model

# 设置API密钥
os.environ["GOOGLE_API_KEY"] = "AIzaSyCRMfVZuUDwLGH2gMDNYV5sawu5TbpMZSI"
llm = init_chat_model("gemini-2.0-flash", model_provider="google_genai")

# ==================== 文件操作工具 ====================

@tool
def list_files(directory: str = ".") -> str:
    """列出目录中的文件"""
    path, error = get_safe_path(directory)
    if error:
        return error

    try:
        if not path.exists():
            return f"目录 '{directory}' 不存在"

        if not path.is_dir():
            return f"'{directory}' 不是一个目录"

        items = []
        for item in sorted(path.iterdir()):
            if item.is_dir():
                items.append(f"📁 {item.name}/")
            else:
                size = item.stat().st_size
                items.append(f"📄 {item.name} ({size} bytes)")

        # 显示相对于工作目录的路径
        relative_path = path.relative_to(WORK_DIR) if path != WORK_DIR else "."
        return f"目录 '{relative_path}' 内容:\n" + "\n".join(items) if items else f"目录 '{relative_path}' 是空的"
    except Exception as e:
        return f"错误: {str(e)}"

@tool
def read_file(filepath: str) -> str:
    """读取文件内容"""
    path, error = get_safe_path(filepath)
    if error:
        return error

    try:
        if not path.exists():
            return f"文件 '{filepath}' 不存在"

        if not path.is_file():
            return f"'{filepath}' 不是一个文件"

        with open(path, 'r', encoding='utf-8') as f:
            content = f.read()

        relative_path = path.relative_to(WORK_DIR)
        return f"文件 '{relative_path}' 内容:\n{content}"
    except UnicodeDecodeError:
        return f"错误：文件 '{filepath}' 不是文本文件或编码不支持"
    except Exception as e:
        return f"读取文件错误: {str(e)}"

@tool
def create_file(filepath: str, content: str = "") -> str:
    """创建新文件"""
    path, error = get_safe_path(filepath)
    if error:
        return error

    try:
        # 创建父目录（如果不存在）
        path.parent.mkdir(parents=True, exist_ok=True)

        if path.exists():
            relative_path = path.relative_to(WORK_DIR)
            return f"文件 '{relative_path}' 已存在"

        with open(path, 'w', encoding='utf-8') as f:
            f.write(content)

        relative_path = path.relative_to(WORK_DIR)
        return f"✅ 文件 '{relative_path}' 创建成功"
    except Exception as e:
        return f"创建文件错误: {str(e)}"

@tool
def delete_file(filepath: str) -> str:
    """删除文件 - 危险操作"""
    try:
        path = Path(filepath)
        if not path.exists():
            return f"文件 '{filepath}' 不存在"
        
        # 创建备份
        backup_dir = Path("./backups")
        backup_dir.mkdir(exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = backup_dir / f"{path.name}_{timestamp}.backup"
        
        shutil.copy2(path, backup_path)
        path.unlink()
        
        return f"✅ 文件 '{filepath}' 已删除，备份: {backup_path}"
    except Exception as e:
        return f"删除文件错误: {str(e)}"

@tool
def human_approval(question: str) -> str:
    """请求人类确认"""
    return question

# ==================== 代理设置 ====================

DANGEROUS_TOOLS = ["delete_file"]
tools = [list_files, read_file, create_file, delete_file, human_approval]
llm_with_tools = llm.bind_tools(tools)

def agent_node(state: MessagesState):
    """代理节点"""
    print("🤖 思考中...")

    # 添加系统消息
    messages = state["messages"]
    if not any(isinstance(msg, SystemMessage) for msg in messages):
        system_msg = SystemMessage(content="""
你是文件管理助手。可用工具：
- list_files: 列出目录
- read_file: 读取文件
- create_file: 创建文件
- delete_file: 删除文件（危险操作）
- human_approval: 请求人类确认

重要规则：
1. 对于删除文件操作，你必须先调用 human_approval 工具请求用户确认
2. 确认问题要清楚说明将要执行的操作
3. 只有在用户确认后，才能调用实际的删除工具
""")
        messages = [system_msg] + messages

    response = llm_with_tools.invoke(messages)
    return {"messages": [response]}

def human_approval_node(state: MessagesState):
    """人类确认节点"""
    return {"messages": []}

def router(state: MessagesState) -> Literal["tools", "__end__", "human_approval"]:
    """路由决策"""
    last_message = state["messages"][-1]

    if not last_message.tool_calls:
        return "__end__"

    tool_name = last_message.tool_calls[0].get("name")
    print(f"🔧 工具调用: {tool_name}")

    if tool_name == "human_approval":
        return "human_approval"
    else:
        return "tools"

# ==================== 构建图 ====================

tool_node = ToolNode(tools)
graph_builder = StateGraph(MessagesState)

graph_builder.add_node("agent", agent_node)
graph_builder.add_node("tools", tool_node)
graph_builder.add_node("human_approval", human_approval_node)

graph_builder.set_entry_point("agent")
graph_builder.add_conditional_edges("agent", router, {
    "tools": "tools",
    "human_approval": "human_approval",
    "__end__": END,
})
graph_builder.add_edge("tools", "agent")
graph_builder.add_edge("human_approval", "agent")

memory = MemorySaver()
graph = graph_builder.compile(
    checkpointer=memory,
    interrupt_before=["human_approval"]
)

# ==================== 主程序 ====================

def main():
    print("🎉 文件管理代理启动！")
    print("可以要求我：列出文件、读取文件、创建文件、删除文件")
    print("输入 'quit' 退出\n")
    
    config = {"configurable": {"thread_id": "session"}}
    
    while True:
        try:
            user_input = input("👤 你: ").strip()
            
            if user_input.lower() in ['quit', 'exit']:
                print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            print(f"\n🚀 处理: {user_input}")
            print("-" * 40)
            
            # 执行请求
            for event in graph.stream({"messages": [HumanMessage(content=user_input)]}, config):
                for node_name, node_output in event.items():
                    if node_name == "agent" and "messages" in node_output:
                        msg = node_output["messages"][-1]
                        if hasattr(msg, 'content') and msg.content:
                            print(f"🤖 {msg.content}")
                    elif node_name == "tools" and "messages" in node_output:
                        msg = node_output["messages"][-1]
                        if hasattr(msg, 'content') and msg.content:
                            print(f"🔧 {msg.content}")
            
            # 检查是否需要确认
            state = graph.get_state(config)
            if state.next and "human_approval" in state.next:
                last_message = state.values["messages"][-1]
                if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                    tool_call = last_message.tool_calls[0]
                    
                    print(f"\n⚠️ 确认危险操作:")
                    print(f"工具: {tool_call['name']}")
                    print(f"参数: {tool_call['args']}")
                    
                    confirm = input("继续执行？(yes/no): ").strip().lower()
                    
                    if confirm in ['yes', 'y']:
                        # 继续执行
                        tool_call_id = tool_call["id"]
                        for event in graph.stream(
                            {"messages": [ToolMessage(content="确认执行", tool_call_id=tool_call_id)]},
                            config
                        ):
                            for node_name, node_output in event.items():
                                if "messages" in node_output:
                                    msg = node_output["messages"][-1]
                                    if hasattr(msg, 'content') and msg.content:
                                        if node_name == "tools":
                                            print(f"🔧 {msg.content}")
                                        elif node_name == "agent":
                                            print(f"🤖 {msg.content}")
                    else:
                        print("❌ 操作已取消")
                        tool_call_id = tool_call["id"]
                        graph.stream(
                            {"messages": [ToolMessage(content="用户取消操作", tool_call_id=tool_call_id)]},
                            config
                        )
            
            print("\n" + "="*40 + "\n")
            
        except KeyboardInterrupt:
            print("\n👋 程序中断，再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
