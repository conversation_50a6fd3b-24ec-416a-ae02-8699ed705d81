from langgraph.graph import StateGraph, END, MessagesState
from langgraph.prebuilt import ToolNode
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.messages import SystemMessage, ToolMessage
import uuid
from langchain_core.tools import tool
from typing import Literal

# 这是一个模拟的危险工具
@tool
def delete_file(filename: str):
    """Deletes a file from the system."""
    print(f"--- 危险操作！已删除文件: {filename} ---")
    return f"File '{filename}' has been successfully deleted."

# 这是关键！一个“请求确认”的工具
# 它不执行任何操作，只是返回一个需要被人类回答的问题。
@tool
def human_approval(question: str) -> str:
    """
    Asks the user for approval to proceed with a critical action.
    Use this to confirm dangerous operations.
    """
    # 这个工具的“实现”就是简单地返回它的输入问题
    # 真正的魔法发生在图的逻辑中
    return question

GOOGLE_API_KEY = "AIzaSyCRMfVZuUDwLGH2gMDNYV5sawu5TbpMZSI"

# 将所有工具给 Agent
tools = [delete_file, human_approval]
llm_with_tools = llm.bind_tools(tools)

# 1. 定义 Agent 节点 (思考者)
def agent_node(state: MessagesState):
    print("--- Agent 节点: 思考中... ---")
    response = llm_with_tools.invoke(state["messages"])
    return {"messages": [response]}

# 2. 定义 Tool 节点 (行动者)
tool_node = ToolNode(tools)

# 3. 关键！定义我们自己的决策逻辑 (取代 tools_condition)
def router(state: MessagesState) -> Literal["tools", "__end__", "human_approval"]:
    last_message = state["messages"][-1]
    # 如果没有工具调用，就结束
    if not last_message.tool_calls:
        return "__end__"
    
    # 如果调用的工具是 human_approval，就走向一个特殊路径
    if last_message.tool_calls[0].get("name") == "human_approval":
        return "human_approval"
    
    # 否则，正常走向工具执行节点
    return "tools"

# 4. 搭建图
graph_builder = StateGraph(MessagesState)
graph_builder.add_node("agent", agent_node)
graph_builder.add_node("tools", tool_node)

# 我们在这里不为 "human_approval" 添加节点，因为它只是一个中断信号
# 我们将通过中断图的执行来处理它

graph_builder.set_entry_point("agent")

# 添加我们自定义的条件边
graph_builder.add_conditional_edges(
    "agent",
    router,
    # 这里定义了三条路径
    {
        "tools": "tools",
        "human_approval": END, # 注意！我们让它走向 END，但我们会中断它
        "__end__": END,
    }
)
graph_builder.add_edge("tools", "agent") # 工具执行完后，循环回 Agent

# 5. 编译时加入中断逻辑
# 在 `human_approval` 路径上设置中断
memory = MemorySaver()
graph = graph_builder.compile(
    checkpointer=memory,
    interrupt_before=["human_approval"], # 当流程即将走向 human_approval 时，暂停！
)

# 6. 运行与交互
config = {"configurable": {"thread_id": "human-in-loop-1"}}
user_input = "Please delete the file 'important_document.txt'"

# 第一次运行，它会停在需要确认的地方
print("\n--- [案例一] 开始执行，等待人类确认 ---")
events = graph.stream({"messages": [{"role": "user", "content": user_input}]}, config)
for event in events:
    if "messages" in event:
        event["messages"][-1].pretty_print()

# 此时，图已经暂停了。我们可以检查状态
# snapshot = graph.get_state(config)
# last_message = snapshot.values['messages'][-1]
# question_to_human = last_message.tool_calls[0]['args']['question']

# 模拟人类确认
print("\n--- 人类已介入，输入 'yes' ---")
# 我们将批准的结果作为一个 ToolMessage 添加回去，然后继续执行
human_response = "yes, I approve."
# 我们需要 tool_call_id 来正确地回应
snapshot = graph.get_state(config)
tool_call_id = snapshot.values['messages'][-1].tool_calls[0]['id']

# 继续图的执行
events = graph.stream(
    None, # 传入 None 表示从上次中断的地方继续
    config,
    input={"messages": [ToolMessage(content=human_response, tool_call_id=tool_call_id)]}
)
for event in events:
    if "messages" in event:
        event["messages"][-1].pretty_print()