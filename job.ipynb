def akargs(city, country, *args, content, **kwargs):
    print(city, country, args, content, kwargs)
    for arg in args:
        print(arg)
    for key, value in kwargs.items():
        print(key, value)


akargs("beijing", "china", "a", "b", "c", content="d", e="f", g="h")

from time import time
import functools

def get_run_time(func):
    @functools.wraps(func)
    def wrapper(*args,**kargs):
        print("start time:",time())
        func(*args,**kargs)
        print("end time:",time())
    return wrapper


from time import sleep

@get_run_time
def test():
    print("test")
    sleep(2)
    print("test end")

test()

class 