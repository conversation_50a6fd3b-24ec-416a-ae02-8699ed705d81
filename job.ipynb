{"cells": [{"cell_type": "markdown", "id": "c5fa8bb6", "metadata": {}, "source": ["#关键字参数"]}, {"cell_type": "code", "execution_count": 1, "id": "669b4150", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["beijing china ('a', 'b', 'c') d {'e': 'f', 'g': 'h'}\n", "a\n", "b\n", "c\n", "e f\n", "g h\n"]}], "source": ["def akargs(city, country, *args, content, **kwargs):\n", "    print(city, country, args, content, kwargs)\n", "    for arg in args:\n", "        print(arg)\n", "    for key, value in kwargs.items():\n", "        print(key, value)\n", "\n", "\n", "akargs(\"beijing\", \"china\", \"a\", \"b\", \"c\", content=\"d\", e=\"f\", g=\"h\")"]}, {"cell_type": "code", "execution_count": 2, "id": "930c651f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["start time: 1753410435.479809\n", "test\n", "test end\n", "end time: 1753410437.4867735\n"]}], "source": ["from time import time\n", "import functools\n", "\n", "def get_run_time(func):\n", "    @functools.wraps(func)\n", "    def wrapper(*args,**kargs):\n", "        print(\"start time:\",time())\n", "        func(*args,**kargs)\n", "        print(\"end time:\",time())\n", "    return wrapper\n", "\n", "\n", "from time import sleep\n", "\n", "@get_run_time\n", "def test():\n", "    print(\"test\")\n", "    sleep(2)\n", "    print(\"test end\")\n", "\n", "test()"]}, {"cell_type": "code", "execution_count": null, "id": "ed04751d", "metadata": {}, "outputs": [], "source": ["class "]}], "metadata": {"kernelspec": {"display_name": "my-langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}