class LangGraphDebugger:
    """LangGraph 调试工具包"""
    
    def __init__(self, app):
        self.app = app
        self.step_count = 0
    
    def debug_run(self, input_data, config, verbose=True):
        """完整的调试运行"""
        print("🎬 开始调试运行")
        print("=" * 60)
        
        try:
            for event in self.app.stream(input_data, config, stream_mode="values"):
                self.step_count += 1
                
                if verbose:
                    self._print_step(event)
                
                # 检查是否需要人工干预
                if self._check_interrupt(event):
                    print("⏸️ 检测到中断，等待人工处理...")
                    break
                    
        except Exception as e:
            print(f"❌ 执行出错: {e}")
            self._print_error_context(e)
    
    def _print_step(self, event):
        """打印步骤信息"""
        print(f"\n📍 步骤 {self.step_count}:")
        
        if "messages" in event:
            msgs = event["messages"]
            print(f"   消息总数: {len(msgs)}")
            
            # 显示最新消息
            if msgs:
                latest = msgs[-1]
                print(f"   最新消息: {type(latest).__name__}")
                print(f"   内容预览: {latest.content[:80]}...")
                
                # 工具调用信息
                if hasattr(latest, 'tool_calls') and latest.tool_calls:
                    tools = [tc['name'] for tc in latest.tool_calls]
                    print(f"   工具调用: {tools}")
    
    def _check_interrupt(self, event):
        """检查是否有中断"""
        # 这里可以添加中断检测逻辑
        return False
    
    def _print_error_context(self, error):
        """打印错误上下文"""
        print(f"\n🔍 错误上下文:")
        print(f"   错误类型: {type(error).__name__}")
        print(f"   错误信息: {str(error)}")
        print(f"   当前步骤: {self.step_count}")

